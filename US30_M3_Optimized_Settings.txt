# MTR Expert Advisor - Optimized Settings for US30 M3 Timeframe
# Copy these settings when attaching the EA to US30 3-minute chart

=== MTR Strategy Settings ===
InpAggressiveEntry=true
InpConservativeEntry=true
InpRiskRewardRatio=2.0
InpMagicNumber=123456
InpLotSize=0.01
InpAutoAdaptive=true

=== Trend Detection (Auto-Adaptive) ===
InpTrendEMA=0          # Auto: M3=8 periods
InpTrailEMA=0          # Auto: M3=5 periods

=== MTR Pattern Settings (Auto-Adaptive) ===
InpSwingBars=0         # Auto: M3=1 bar
InpMinBreakPoints=0    # Auto: M3=25 points
InpMaxFailPoints=0     # Auto: M3=15 points
InpBodyRatioSmall=0.35 # US30 optimized
InpBodyRatioLarge=0.65 # US30 optimized
InpWickRatio=1.3       # US30 optimized
InpPullbackPoints=0    # Auto: M3=40 points

=== Risk Management (Auto-Adaptive) ===
InpStopLossPoints=0    # Auto: M3=20 points
InpMaxSpreadPoints=0   # Auto: M3=30 points
InpRiskPercent=1.0     # 1% risk per trade

=== Explanation of Auto-Adaptive Values for US30 M3 ===

When InpAutoAdaptive=true, the EA automatically sets:

Trend Detection:
- Trend EMA: 8 periods (faster response for M3)
- Trail EMA: 5 periods (tight trailing for quick moves)

Pattern Recognition:
- Swing Bars: 1 (M3 needs faster confirmation)
- Min Break: 25 points (US30 typical move size)
- Max Fail: 15 points (tight failure tolerance)
- Pullback: 40 points (reasonable retracement zone)

Risk Management:
- Stop Buffer: 20 points (account for US30 volatility)
- Max Spread: 30 points (filter high spread periods)

=== US30 Characteristics Considered ===

1. High Volatility: US30 moves in larger point increments
2. Fast Movement: M3 timeframe requires quick decisions
3. Index Behavior: Different from forex pairs
4. Session Sensitivity: Most active during US market hours
5. News Impact: Highly sensitive to economic announcements

=== Recommended Trading Hours (EST) ===
- Best: 9:30 AM - 4:00 PM (US Market Hours)
- Good: 8:00 AM - 6:00 PM (Extended Hours)
- Avoid: 6:00 PM - 8:00 AM (Low Volume)

=== Additional Tips for US30 M3 ===

1. Monitor during high-impact news releases
2. Be aware of market opening gaps
3. Consider reducing lot size during volatile periods
4. Use VPS for consistent execution
5. Backtest thoroughly before live trading

=== Expected Performance Characteristics ===

- Trade Frequency: 2-5 trades per day
- Average Risk: 20-40 points
- Average Reward: 40-80 points (2:1 R:R)
- Win Rate Target: 45-55%
- Drawdown: Monitor for 5-10% maximum

=== Monitoring Checklist ===

Daily:
□ Check spread conditions
□ Verify EA is running
□ Review overnight positions
□ Check economic calendar

Weekly:
□ Analyze trade performance
□ Review parameter effectiveness
□ Check for any pattern changes
□ Update risk management if needed

Monthly:
□ Full performance review
□ Consider parameter optimization
□ Evaluate market condition changes
□ Update trading plan if necessary
