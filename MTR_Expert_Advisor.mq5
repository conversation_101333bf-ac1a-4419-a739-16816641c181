//+------------------------------------------------------------------+
//|                                           MTR_Expert_Advisor.mq5 |
//|                        Copyright 2024, <PERSON>TR Strategy EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Al <PERSON> MTR Strategy EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Major Trend Reversal Expert Advisor based on Al <PERSON> Price Action"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Create trade objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//--- Input parameters
input group "=== MTR Strategy Settings ==="
input bool     InpAggressiveEntry = true;        // Enable Aggressive Entry
input bool     InpConservativeEntry = true;      // Enable Conservative Entry
input double   InpRiskRewardRatio = 2.0;         // Risk:Reward Ratio for TP1 (US30 optimized)
input int      InpMagicNumber = 123456;          // Magic Number
input double   InpLotSize = 0.01;                // Lot Size (US30 optimized)
input bool     InpAutoAdaptive = true;           // Enable Auto-Adaptive Parameters

input group "=== Trend Detection (Auto-Adaptive) ==="
input int      InpTrendEMA = 0;                  // Trend EMA Period (0=Auto: M3=8, M5=12, M15=20, H1=34)
input int      InpTrailEMA = 0;                  // Trailing EMA Period (0=Auto: M3=5, M5=8, M15=13, H1=21)

input group "=== MTR Pattern Settings (Auto-Adaptive) ==="
input int      InpSwingBars = 0;                 // Swing Confirmation Bars (0=Auto: M3=1, M5=2, M15=2, H1=3)
input double   InpMinBreakPoints = 0;            // Min Break Above Top1 (0=Auto, points)
input double   InpMaxFailPoints = 0;             // Max Fail Above Top2 (0=Auto, points)
input double   InpBodyRatioSmall = 0.35;         // Small Body Ratio (US30 optimized)
input double   InpBodyRatioLarge = 0.65;         // Large Body Ratio (US30 optimized)
input double   InpWickRatio = 1.3;               // Upper Wick Ratio (US30 optimized)
input double   InpPullbackPoints = 0;            // Pullback Range (0=Auto, points)

input group "=== Risk Management (Auto-Adaptive) ==="
input double   InpStopLossPoints = 0;            // Stop Loss Buffer (0=Auto, points)
input double   InpMaxSpreadPoints = 0;           // Maximum Spread (0=Auto, points)
input double   InpRiskPercent = 1.0;             // Risk per trade (% of account)

//--- Global variables
int            trendEMA_handle;
int            trailEMA_handle;
double         trendEMA_buffer[];
double         trailEMA_buffer[];

datetime       lastBarTime = 0;
bool           patternDetected = false;
bool           waitingForPullback = false;

//--- Adaptive parameters (calculated automatically)
int            adaptiveTrendEMA;
int            adaptiveTrailEMA;
int            adaptiveSwingBars;
double         adaptiveMinBreak;
double         adaptiveMaxFail;
double         adaptivePullback;
double         adaptiveStopBuffer;
double         adaptiveMaxSpread;
double         symbolPointValue;
bool           isIndexSymbol;
string         symbolGroup;

//--- Adaptive parameters (calculated automatically)
int            adaptiveTrendEMA;
int            adaptiveTrailEMA;
int            adaptiveSwingBars;
double         adaptiveMinBreak;
double         adaptiveMaxFail;
double         adaptivePullback;
double         adaptiveStopBuffer;
double         adaptiveMaxSpread;
double         symbolPointValue;
bool           isIndexSymbol;
string         symbolGroup;

// MTR Pattern Structure
struct MTRPattern
{
   datetime    top1_time;
   double      top1_high;
   datetime    top2_time;
   double      top2_high;
   datetime    top3_time;
   double      top3_high;
   double      breakout_low;
   bool        is_valid;
   bool        is_uptrend_reversal;
   bool        aggressive_triggered;
   bool        conservative_triggered;
};

MTRPattern currentMTR;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Calculate adaptive parameters
   if(InpAutoAdaptive)
   {
      CalculateAdaptiveParameters();
   }
   else
   {
      // Use manual parameters
      adaptiveTrendEMA = (InpTrendEMA > 0) ? InpTrendEMA : 20;
      adaptiveTrailEMA = (InpTrailEMA > 0) ? InpTrailEMA : 13;
      adaptiveSwingBars = (InpSwingBars > 0) ? InpSwingBars : 2;
      adaptiveMinBreak = (InpMinBreakPoints > 0) ? InpMinBreakPoints : 30;
      adaptiveMaxFail = (InpMaxFailPoints > 0) ? InpMaxFailPoints : 20;
      adaptivePullback = (InpPullbackPoints > 0) ? InpPullbackPoints : 50;
      adaptiveStopBuffer = (InpStopLossPoints > 0) ? InpStopLossPoints : 20;
      adaptiveMaxSpread = (InpMaxSpreadPoints > 0) ? InpMaxSpreadPoints : 30;
   }

   //--- Initialize indicators with adaptive periods
   trendEMA_handle = iMA(_Symbol, PERIOD_CURRENT, adaptiveTrendEMA, 0, MODE_EMA, PRICE_CLOSE);
   trailEMA_handle = iMA(_Symbol, PERIOD_CURRENT, adaptiveTrailEMA, 0, MODE_EMA, PRICE_CLOSE);

   if(trendEMA_handle == INVALID_HANDLE || trailEMA_handle == INVALID_HANDLE)
   {
      Print("Error creating indicators");
      return(INIT_FAILED);
   }

   //--- Set array as series
   ArraySetAsSeries(trendEMA_buffer, true);
   ArraySetAsSeries(trailEMA_buffer, true);

   //--- Initialize trade settings
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(10);
   trade.SetTypeFilling(ORDER_FILLING_FOK);

   //--- Initialize MTR pattern
   ResetMTRPattern();

   //--- Print adaptive parameters
   PrintAdaptiveParameters();

   Print("MTR Expert Advisor initialized successfully for ", _Symbol, " ", EnumToString(PERIOD_CURRENT));
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(trendEMA_handle != INVALID_HANDLE)
      IndicatorRelease(trendEMA_handle);
   if(trailEMA_handle != INVALID_HANDLE)
      IndicatorRelease(trailEMA_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check for new bar
   if(!IsNewBar())
      return;
      
   //--- Check if we already have a position
   if(PositionsTotal() > 0)
   {
      ManageOpenPositions();
      return;
   }
   
   //--- Update indicator buffers
   if(!UpdateIndicators())
      return;
      
   //--- Check spread
   if(GetSpreadInPoints() > adaptiveMaxSpread)
      return;
      
   //--- Detect trend direction
   ENUM_TREND_DIRECTION trendDirection = GetTrendDirection();
   
   //--- Look for MTR patterns
   if(trendDirection == TREND_UP)
   {
      DetectMTRUptrend();
   }
   else if(trendDirection == TREND_DOWN)
   {
      DetectMTRDowntrend();
   }
   
   //--- Check for entry signals
   CheckEntrySignals();

   //--- Update comment display
   UpdateComment();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update indicator buffers                                         |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   if(CopyBuffer(trendEMA_handle, 0, 0, 50, trendEMA_buffer) < 0)
      return false;
   if(CopyBuffer(trailEMA_handle, 0, 0, 50, trailEMA_buffer) < 0)
      return false;
   return true;
}

//+------------------------------------------------------------------+
//| Get spread in points                                             |
//+------------------------------------------------------------------+
double GetSpreadInPoints()
{
   return (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
}

//+------------------------------------------------------------------+
//| Trend direction enumeration                                      |
//+------------------------------------------------------------------+
enum ENUM_TREND_DIRECTION
{
   TREND_UP,
   TREND_DOWN,
   TREND_SIDEWAYS
};

//+------------------------------------------------------------------+
//| Get trend direction based on EMA                                 |
//+------------------------------------------------------------------+
ENUM_TREND_DIRECTION GetTrendDirection()
{
   double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 1);
   double ema_current = trendEMA_buffer[1];
   double ema_previous = trendEMA_buffer[5];
   
   // Check if price is mostly above/below EMA and EMA angle
   bool priceAboveEMA = currentPrice > ema_current;
   bool emaRising = ema_current > ema_previous;
   
   if(priceAboveEMA && emaRising)
      return TREND_UP;
   else if(!priceAboveEMA && !emaRising)
      return TREND_DOWN;
   else
      return TREND_SIDEWAYS;
}

//+------------------------------------------------------------------+
//| Reset MTR pattern structure                                      |
//+------------------------------------------------------------------+
void ResetMTRPattern()
{
   currentMTR.top1_time = 0;
   currentMTR.top1_high = 0;
   currentMTR.top2_time = 0;
   currentMTR.top2_high = 0;
   currentMTR.top3_time = 0;
   currentMTR.top3_high = 0;
   currentMTR.breakout_low = 0;
   currentMTR.is_valid = false;
   currentMTR.is_uptrend_reversal = false;
   currentMTR.aggressive_triggered = false;
   currentMTR.conservative_triggered = false;
   
   patternDetected = false;
   waitingForPullback = false;
}

//+------------------------------------------------------------------+
//| Calculate adaptive parameters based on symbol and timeframe     |
//+------------------------------------------------------------------+
void CalculateAdaptiveParameters()
{
   //--- Analyze symbol characteristics
   AnalyzeSymbolCharacteristics();

   //--- Get current timeframe
   ENUM_TIMEFRAMES tf = PERIOD_CURRENT;

   //--- Calculate EMA periods based on timeframe
   switch(tf)
   {
      case PERIOD_M1:
         adaptiveTrendEMA = 5;
         adaptiveTrailEMA = 3;
         adaptiveSwingBars = 1;
         break;
      case PERIOD_M3:  // Optimized for US30 M3
         adaptiveTrendEMA = 8;
         adaptiveTrailEMA = 5;
         adaptiveSwingBars = 1;
         break;
      case PERIOD_M5:
         adaptiveTrendEMA = 12;
         adaptiveTrailEMA = 8;
         adaptiveSwingBars = 2;
         break;
      case PERIOD_M15:
         adaptiveTrendEMA = 20;
         adaptiveTrailEMA = 13;
         adaptiveSwingBars = 2;
         break;
      case PERIOD_M30:
         adaptiveTrendEMA = 25;
         adaptiveTrailEMA = 16;
         adaptiveSwingBars = 2;
         break;
      case PERIOD_H1:
         adaptiveTrendEMA = 34;
         adaptiveTrailEMA = 21;
         adaptiveSwingBars = 3;
         break;
      case PERIOD_H4:
         adaptiveTrendEMA = 50;
         adaptiveTrailEMA = 30;
         adaptiveSwingBars = 3;
         break;
      default:
         adaptiveTrendEMA = 20;
         adaptiveTrailEMA = 13;
         adaptiveSwingBars = 2;
         break;
   }

   //--- Calculate distance parameters based on symbol type and timeframe
   if(isIndexSymbol) // US30, NAS100, SPX500, etc.
   {
      switch(tf)
      {
         case PERIOD_M1:
            adaptiveMinBreak = 15;
            adaptiveMaxFail = 10;
            adaptivePullback = 25;
            adaptiveStopBuffer = 15;
            adaptiveMaxSpread = 20;
            break;
         case PERIOD_M3:  // US30 M3 optimized
            adaptiveMinBreak = 25;
            adaptiveMaxFail = 15;
            adaptivePullback = 40;
            adaptiveStopBuffer = 20;
            adaptiveMaxSpread = 30;
            break;
         case PERIOD_M5:
            adaptiveMinBreak = 35;
            adaptiveMaxFail = 20;
            adaptivePullback = 60;
            adaptiveStopBuffer = 25;
            adaptiveMaxSpread = 40;
            break;
         case PERIOD_M15:
            adaptiveMinBreak = 60;
            adaptiveMaxFail = 35;
            adaptivePullback = 100;
            adaptiveStopBuffer = 40;
            adaptiveMaxSpread = 60;
            break;
         case PERIOD_M30:
            adaptiveMinBreak = 100;
            adaptiveMaxFail = 60;
            adaptivePullback = 150;
            adaptiveStopBuffer = 60;
            adaptiveMaxSpread = 80;
            break;
         case PERIOD_H1:
            adaptiveMinBreak = 150;
            adaptiveMaxFail = 100;
            adaptivePullback = 250;
            adaptiveStopBuffer = 100;
            adaptiveMaxSpread = 120;
            break;
         default:
            adaptiveMinBreak = 60;
            adaptiveMaxFail = 35;
            adaptivePullback = 100;
            adaptiveStopBuffer = 40;
            adaptiveMaxSpread = 60;
            break;
      }
   }
   else // Forex pairs
   {
      double pipValue = GetPipValue();
      switch(tf)
      {
         case PERIOD_M1:
            adaptiveMinBreak = 3 * pipValue;
            adaptiveMaxFail = 2 * pipValue;
            adaptivePullback = 5 * pipValue;
            adaptiveStopBuffer = 2 * pipValue;
            adaptiveMaxSpread = 3 * pipValue;
            break;
         case PERIOD_M3:
            adaptiveMinBreak = 5 * pipValue;
            adaptiveMaxFail = 3 * pipValue;
            adaptivePullback = 8 * pipValue;
            adaptiveStopBuffer = 3 * pipValue;
            adaptiveMaxSpread = 4 * pipValue;
            break;
         case PERIOD_M5:
            adaptiveMinBreak = 8 * pipValue;
            adaptiveMaxFail = 5 * pipValue;
            adaptivePullback = 12 * pipValue;
            adaptiveStopBuffer = 4 * pipValue;
            adaptiveMaxSpread = 5 * pipValue;
            break;
         case PERIOD_M15:
            adaptiveMinBreak = 12 * pipValue;
            adaptiveMaxFail = 8 * pipValue;
            adaptivePullback = 20 * pipValue;
            adaptiveStopBuffer = 6 * pipValue;
            adaptiveMaxSpread = 8 * pipValue;
            break;
         default:
            adaptiveMinBreak = 12 * pipValue;
            adaptiveMaxFail = 8 * pipValue;
            adaptivePullback = 20 * pipValue;
            adaptiveStopBuffer = 6 * pipValue;
            adaptiveMaxSpread = 8 * pipValue;
            break;
      }
   }

   //--- Apply user overrides if specified
   if(InpMinBreakPoints > 0) adaptiveMinBreak = InpMinBreakPoints;
   if(InpMaxFailPoints > 0) adaptiveMaxFail = InpMaxFailPoints;
   if(InpPullbackPoints > 0) adaptivePullback = InpPullbackPoints;
   if(InpStopLossPoints > 0) adaptiveStopBuffer = InpStopLossPoints;
   if(InpMaxSpreadPoints > 0) adaptiveMaxSpread = InpMaxSpreadPoints;
}

//+------------------------------------------------------------------+
//| Analyze symbol characteristics                                   |
//+------------------------------------------------------------------+
void AnalyzeSymbolCharacteristics()
{
   string symbol = _Symbol;

   //--- Determine symbol type
   if(StringFind(symbol, "US30") >= 0 || StringFind(symbol, "DOW") >= 0 ||
      StringFind(symbol, "NAS") >= 0 || StringFind(symbol, "SPX") >= 0 ||
      StringFind(symbol, "DAX") >= 0 || StringFind(symbol, "FTSE") >= 0 ||
      StringFind(symbol, "NIKKEI") >= 0 || StringFind(symbol, "ASX") >= 0)
   {
      isIndexSymbol = true;
      symbolGroup = "INDEX";
   }
   else if(StringFind(symbol, "USD") >= 0 || StringFind(symbol, "EUR") >= 0 ||
           StringFind(symbol, "GBP") >= 0 || StringFind(symbol, "JPY") >= 0 ||
           StringFind(symbol, "CHF") >= 0 || StringFind(symbol, "CAD") >= 0 ||
           StringFind(symbol, "AUD") >= 0 || StringFind(symbol, "NZD") >= 0)
   {
      isIndexSymbol = false;
      symbolGroup = "FOREX";
   }
   else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0 ||
           StringFind(symbol, "XAG") >= 0 || StringFind(symbol, "SILVER") >= 0)
   {
      isIndexSymbol = false;
      symbolGroup = "METALS";
   }
   else if(StringFind(symbol, "WTI") >= 0 || StringFind(symbol, "BRENT") >= 0 ||
           StringFind(symbol, "OIL") >= 0)
   {
      isIndexSymbol = false;
      symbolGroup = "ENERGY";
   }
   else
   {
      isIndexSymbol = false;
      symbolGroup = "OTHER";
   }

   //--- Get symbol point value
   symbolPointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
}

//+------------------------------------------------------------------+
//| Get pip value for the symbol                                     |
//+------------------------------------------------------------------+
double GetPipValue()
{
   string symbol = _Symbol;

   // For most forex pairs, 1 pip = 10 points (0.0001)
   // For JPY pairs, 1 pip = 1 point (0.01)
   if(StringFind(symbol, "JPY") >= 0)
      return 1.0;  // JPY pairs: 1 pip = 1 point
   else
      return 10.0; // Other forex: 1 pip = 10 points
}

//+------------------------------------------------------------------+
//| Print adaptive parameters                                        |
//+------------------------------------------------------------------+
void PrintAdaptiveParameters()
{
   Print("=== ADAPTIVE PARAMETERS ===");
   Print("Symbol: ", _Symbol, " (", symbolGroup, ")");
   Print("Timeframe: ", EnumToString(PERIOD_CURRENT));
   Print("Trend EMA: ", adaptiveTrendEMA);
   Print("Trail EMA: ", adaptiveTrailEMA);
   Print("Swing Bars: ", adaptiveSwingBars);
   Print("Min Break: ", adaptiveMinBreak, " points");
   Print("Max Fail: ", adaptiveMaxFail, " points");
   Print("Pullback: ", adaptivePullback, " points");
   Print("Stop Buffer: ", adaptiveStopBuffer, " points");
   Print("Max Spread: ", adaptiveMaxSpread, " points");
   Print("===========================");
}

//+------------------------------------------------------------------+
//| Detect MTR pattern in uptrend (for short entry)                 |
//+------------------------------------------------------------------+
void DetectMTRUptrend()
{
   // Look for three consecutive tops with specific characteristics
   if(!currentMTR.is_valid)
   {
      // Search for Top1: swing high with confirmation
      int top1_index = FindSwingHigh(adaptiveSwingBars, 10);
      if(top1_index > 0)
      {
         currentMTR.top1_time = iTime(_Symbol, PERIOD_CURRENT, top1_index);
         currentMTR.top1_high = iHigh(_Symbol, PERIOD_CURRENT, top1_index);

         // Search for Top2: breaks above Top1 with specific candle characteristics
         int top2_index = FindTop2(top1_index);
         if(top2_index > 0)
         {
            currentMTR.top2_time = iTime(_Symbol, PERIOD_CURRENT, top2_index);
            currentMTR.top2_high = iHigh(_Symbol, PERIOD_CURRENT, top2_index);

            // Search for Top3: fails to break Top2 significantly
            int top3_index = FindTop3(top2_index);
            if(top3_index > 0)
            {
               currentMTR.top3_time = iTime(_Symbol, PERIOD_CURRENT, top3_index);
               currentMTR.top3_high = iHigh(_Symbol, PERIOD_CURRENT, top3_index);

               // Find breakout low between Top2 and Top3
               currentMTR.breakout_low = FindBreakoutLow(top2_index, top3_index);

               currentMTR.is_valid = true;
               currentMTR.is_uptrend_reversal = true;
               patternDetected = true;

               Print("MTR Uptrend Reversal Pattern Detected!");
               SendAlert("MTR Pattern Detected", "Uptrend Reversal Pattern Found");
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Detect MTR pattern in downtrend (for long entry)                |
//+------------------------------------------------------------------+
void DetectMTRDowntrend()
{
   // Similar logic for downtrend reversal (mirror of uptrend)
   // This would detect three consecutive bottoms for long entries
   // Implementation would be similar but inverted
}

//+------------------------------------------------------------------+
//| Find swing high with confirmation bars                          |
//+------------------------------------------------------------------+
int FindSwingHigh(int confirmBars, int lookbackBars)
{
   for(int i = confirmBars + 1; i < lookbackBars; i++)
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);
      bool isSwingHigh = true;

      // Check bars before
      for(int j = 1; j <= confirmBars; j++)
      {
         if(iHigh(_Symbol, PERIOD_CURRENT, i - j) >= currentHigh)
         {
            isSwingHigh = false;
            break;
         }
      }

      // Check bars after
      if(isSwingHigh)
      {
         for(int j = 1; j <= confirmBars; j++)
         {
            if(iHigh(_Symbol, PERIOD_CURRENT, i + j) >= currentHigh)
            {
               isSwingHigh = false;
               break;
            }
         }
      }

      if(isSwingHigh)
         return i;
   }
   return -1;
}

//+------------------------------------------------------------------+
//| Find Top2 that breaks above Top1 with specific characteristics  |
//+------------------------------------------------------------------+
int FindTop2(int top1_index)
{
   double top1_high = iHigh(_Symbol, PERIOD_CURRENT, top1_index);
   double minBreakDistance = adaptiveMinBreak * _Point;

   for(int i = 1; i < top1_index - 1; i++)
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);

      // Check if breaks above Top1 by minimum distance
      if(currentHigh > top1_high + minBreakDistance)
      {
         // Check candle characteristics: small body and long upper wick
         if(HasSmallBodyLongWick(i))
         {
            return i;
         }
      }
   }
   return -1;
}

//+------------------------------------------------------------------+
//| Find Top3 that fails to break Top2 significantly                |
//+------------------------------------------------------------------+
int FindTop3(int top2_index)
{
   double top2_high = iHigh(_Symbol, PERIOD_CURRENT, top2_index);
   double maxFailDistance = adaptiveMaxFail * _Point;

   for(int i = 1; i < top2_index - 1; i++)
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);

      // Check if fails to break Top2 by more than max distance
      if(currentHigh <= top2_high + maxFailDistance)
      {
         // Check for indecision candle or bearish engulfing
         if(HasIndecisionCandle(i) || IsBearishEngulfing(i))
         {
            return i;
         }
      }
   }
   return -1;
}

//+------------------------------------------------------------------+
//| Check if candle has small body and long upper wick              |
//+------------------------------------------------------------------+
bool HasSmallBodyLongWick(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);

   double bodySize = MathAbs(close - open);
   double totalSize = high - low;
   double upperWick = high - MathMax(open, close);

   if(totalSize == 0) return false;

   double bodyRatio = bodySize / totalSize;
   double wickRatio = (bodySize > 0) ? upperWick / bodySize : 0;

   return (bodyRatio < InpBodyRatioSmall && wickRatio > InpWickRatio);
}

//+------------------------------------------------------------------+
//| Check if candle is indecision candle                            |
//+------------------------------------------------------------------+
bool HasIndecisionCandle(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);

   double bodySize = MathAbs(close - open);
   double totalSize = high - low;

   if(totalSize == 0) return false;

   double bodyRatio = bodySize / totalSize;
   return (bodyRatio < InpBodyRatioSmall);
}

//+------------------------------------------------------------------+
//| Check if candle is bearish engulfing                            |
//+------------------------------------------------------------------+
bool IsBearishEngulfing(int index)
{
   if(index >= Bars(_Symbol, PERIOD_CURRENT) - 1) return false;

   double open1 = iOpen(_Symbol, PERIOD_CURRENT, index + 1);
   double close1 = iClose(_Symbol, PERIOD_CURRENT, index + 1);
   double open2 = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close2 = iClose(_Symbol, PERIOD_CURRENT, index);

   // Previous candle is bullish, current is bearish and engulfs previous
   return (close1 > open1 && close2 < open2 && open2 > close1 && close2 < open1);
}

//+------------------------------------------------------------------+
//| Find breakout low between Top2 and Top3                         |
//+------------------------------------------------------------------+
double FindBreakoutLow(int top2_index, int top3_index)
{
   double lowestLow = DBL_MAX;

   for(int i = top3_index; i <= top2_index; i++)
   {
      double currentLow = iLow(_Symbol, PERIOD_CURRENT, i);
      if(currentLow < lowestLow)
         lowestLow = currentLow;
   }

   return lowestLow;
}

//+------------------------------------------------------------------+
//| Check for entry signals                                         |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
   if(!currentMTR.is_valid || !patternDetected)
      return;

   // Check for breakout below the low between Top2 and Top3
   double currentLow = iLow(_Symbol, PERIOD_CURRENT, 1);
   double currentClose = iClose(_Symbol, PERIOD_CURRENT, 1);
   double currentOpen = iOpen(_Symbol, PERIOD_CURRENT, 1);

   if(currentMTR.is_uptrend_reversal && currentLow < currentMTR.breakout_low)
   {
      // Check if breakout candle has strong bearish body
      if(HasStrongBearishBody(1))
      {
         // Aggressive entry
         if(InpAggressiveEntry && !currentMTR.aggressive_triggered)
         {
            ExecuteShortEntry("Aggressive");
            currentMTR.aggressive_triggered = true;
         }

         // Set flag for conservative entry
         if(InpConservativeEntry && !currentMTR.conservative_triggered)
         {
            waitingForPullback = true;
         }
      }
   }

   // Check for conservative entry (pullback to breakout zone)
   if(waitingForPullback && InpConservativeEntry && !currentMTR.conservative_triggered)
   {
      double pullbackRange = adaptivePullback * _Point;
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, 1);

      // Check if price pulled back to breakout zone
      if(currentHigh >= currentMTR.breakout_low - pullbackRange &&
         currentHigh <= currentMTR.breakout_low + pullbackRange)
      {
         // Check for bearish confirmation candle
         if(HasStrongBearishBody(1))
         {
            ExecuteShortEntry("Conservative");
            currentMTR.conservative_triggered = true;
            waitingForPullback = false;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if candle has strong bearish body                         |
//+------------------------------------------------------------------+
bool HasStrongBearishBody(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);

   if(close >= open) return false; // Not bearish

   double bodySize = open - close;
   double totalSize = high - low;

   if(totalSize == 0) return false;

   double bodyRatio = bodySize / totalSize;
   return (bodyRatio > InpBodyRatioLarge);
}

//+------------------------------------------------------------------+
//| Execute short entry                                             |
//+------------------------------------------------------------------+
void ExecuteShortEntry(string entryType)
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   // Calculate stop loss above Top3 high + buffer
   double stopLoss = currentMTR.top3_high + (adaptiveStopBuffer * _Point);

   // Calculate take profit based on risk:reward ratio
   double riskPoints = (stopLoss - bid) / _Point;
   double rewardPoints = riskPoints * InpRiskRewardRatio;
   double takeProfit = bid - (rewardPoints * _Point);

   // Calculate dynamic lot size based on risk percentage
   double lotSize = CalculateDynamicLotSize(riskPoints);

   // Normalize prices
   stopLoss = NormalizeDouble(stopLoss, _Digits);
   takeProfit = NormalizeDouble(takeProfit, _Digits);

   // Execute trade
   if(trade.Sell(lotSize, _Symbol, bid, stopLoss, takeProfit,
                 StringFormat("MTR %s Short Entry", entryType)))
   {
      Print(StringFormat("SHORT %s Entry Executed: Price=%.5f, SL=%.5f, TP=%.5f, Lot=%.2f",
                        entryType, bid, stopLoss, takeProfit, lotSize));

      SendAlert("Trade Executed",
                StringFormat("MTR %s SHORT: Entry=%.5f, SL=%.5f, TP=%.5f, Risk=%.1f points, Lot=%.2f",
                           entryType, bid, stopLoss, takeProfit, riskPoints, lotSize));

      // Reset pattern after trade execution
      ResetMTRPattern();
   }
   else
   {
      Print("Failed to execute short trade: ", trade.ResultRetcode());
   }
}

//+------------------------------------------------------------------+
//| Manage open positions                                           |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
         {
            // Optional: Implement trailing stop based on 13-period EMA
            // This can be enabled as an additional feature
            ManageTrailingStop();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manage trailing stop based on EMA                               |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
   if(!position.Select(_Symbol)) return;

   double currentEMA = trailEMA_buffer[1];
   double currentSL = position.StopLoss();
   double newSL = 0;

   if(position.PositionType() == POSITION_TYPE_SELL)
   {
      // For short positions, trail stop below EMA
      newSL = currentEMA + (adaptiveStopBuffer * _Point);
      newSL = NormalizeDouble(newSL, _Digits);

      // Only move SL if it's better (lower for short)
      if(newSL < currentSL || currentSL == 0)
      {
         if(trade.PositionModify(_Symbol, newSL, position.TakeProfit()))
         {
            Print("Trailing stop updated to: ", newSL);
         }
      }
   }
   else if(position.PositionType() == POSITION_TYPE_BUY)
   {
      // For long positions, trail stop above EMA
      newSL = currentEMA - (adaptiveStopBuffer * _Point);
      newSL = NormalizeDouble(newSL, _Digits);

      // Only move SL if it's better (higher for long)
      if(newSL > currentSL || currentSL == 0)
      {
         if(trade.PositionModify(_Symbol, newSL, position.TakeProfit()))
         {
            Print("Trailing stop updated to: ", newSL);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Send alert notification                                         |
//+------------------------------------------------------------------+
void SendAlert(string title, string message)
{
   string fullMessage = StringFormat("[%s] %s: %s", _Symbol, title, message);

   // Send to log
   Print(fullMessage);

   // Send push notification (if enabled in terminal)
   SendNotification(fullMessage);

   // Optional: Send email alert
   // SendMail(title, fullMessage);
}

//+------------------------------------------------------------------+
//| Get current time string                                         |
//+------------------------------------------------------------------+
string GetTimeString()
{
   return TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Convert pips to points                                          |
//+------------------------------------------------------------------+
double PipsToPoints(double pips)
{
   return pips * _Point;
}

//+------------------------------------------------------------------+
//| Convert points to pips                                          |
//+------------------------------------------------------------------+
double PointsToPips(double points)
{
   return points / _Point;
}

//+------------------------------------------------------------------+
//| Check if market is open                                         |
//+------------------------------------------------------------------+
bool IsMarketOpen()
{
   return SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL;
}

//+------------------------------------------------------------------+
//| Calculate dynamic lot size based on risk percentage             |
//+------------------------------------------------------------------+
double CalculateDynamicLotSize(double riskPoints)
{
   // If risk percentage is 0 or negative, use fixed lot size
   if(InpRiskPercent <= 0)
      return InpLotSize;

   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * (InpRiskPercent / 100.0);

   // Get symbol specifications
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double contractSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_CONTRACT_SIZE);

   if(tickValue == 0 || tickSize == 0 || contractSize == 0)
      return InpLotSize;

   // Calculate position size
   double riskInPrice = riskPoints * _Point;
   double positionSize;

   if(isIndexSymbol)
   {
      // For indices like US30, calculate based on point value
      double pointValue = tickValue / (tickSize / _Point);
      positionSize = riskAmount / (riskInPrice * pointValue);
   }
   else
   {
      // For forex, use standard calculation
      positionSize = riskAmount / (riskInPrice * tickValue / tickSize);
   }

   // Normalize to symbol specifications
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   positionSize = MathMax(minLot, MathMin(maxLot, positionSize));
   positionSize = NormalizeDouble(positionSize / lotStep, 0) * lotStep;

   return positionSize;
}

//+------------------------------------------------------------------+
//| Expert Advisor Comment Function                                 |
//+------------------------------------------------------------------+
void UpdateComment()
{
   string comment = StringFormat(
      "MTR Expert Advisor - %s (%s)\n" +
      "Time: %s\n" +
      "Spread: %.1f points (Max: %.0f)\n" +
      "Trend EMA(%d): %.5f\n" +
      "Trail EMA(%d): %.5f\n" +
      "Pattern Detected: %s\n" +
      "Waiting Pullback: %s\n" +
      "Positions: %d\n" +
      "Adaptive Settings:\n" +
      "- Swing Bars: %d\n" +
      "- Min Break: %.0f pts\n" +
      "- Max Fail: %.0f pts\n" +
      "- Pullback: %.0f pts\n" +
      "- Stop Buffer: %.0f pts",
      _Symbol,
      symbolGroup,
      GetTimeString(),
      GetSpreadInPoints(),
      adaptiveMaxSpread,
      adaptiveTrendEMA,
      trendEMA_buffer[1],
      adaptiveTrailEMA,
      trailEMA_buffer[1],
      patternDetected ? "YES" : "NO",
      waitingForPullback ? "YES" : "NO",
      PositionsTotal(),
      adaptiveSwingBars,
      adaptiveMinBreak,
      adaptiveMaxFail,
      adaptivePullback,
      adaptiveStopBuffer
   );

   Comment(comment);
}
