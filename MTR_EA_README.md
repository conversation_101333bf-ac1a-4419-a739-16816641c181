# Major <PERSON><PERSON><PERSON> (MTR) Expert Advisor - Auto-Adaptive Version

## Overview
This Expert Advisor implements <PERSON>' Major <PERSON><PERSON><PERSON> (MTR) strategy for MetaTrader 5 with **automatic parameter adaptation** based on symbol type and timeframe. The EA is **optimized for US30 (<PERSON>) on M3 (3-minute) timeframe** but works across all major symbols and timeframes with intelligent parameter scaling.

## Strategy Description
The MTR strategy identifies trend reversal patterns by detecting three consecutive tops (in uptrends) with specific characteristics:

### MTR Pattern Components
1. **Top1**: Swing high with 2 confirmation bars before and after
2. **Top2**: Breaks above Top1 by at least 3 pips but forms a small body candle (<40%) with long upper wick (>1.5x body)
3. **Top3**: Fails to break Top2 by more than 2 pips and forms indecision or bearish engulfing candle
4. **Breakout**: Price breaks below the lowest low between Top2 and Top3 with a strong bearish candle (>60% body)

## Installation Instructions

### 1. Copy Files
- Place `MTR_Expert_Advisor.mq5` in your MetaTrader 5 `MQL5/Experts/` folder
- Restart MetaTrader 5 or refresh the Navigator

### 2. Compile the EA
- Open MetaEditor (F4 in MT5)
- Open the `MTR_Expert_Advisor.mq5` file
- Press F7 to compile
- Fix any compilation errors if they occur

### 3. Attach to Chart
- Open a chart (recommended: H1 or M30 timeframe)
- Drag the EA from Navigator to the chart
- Configure input parameters as needed
- Enable "Allow live trading" and "Allow DLL imports" if required

## Input Parameters

### MTR Strategy Settings
- **Enable Aggressive Entry**: Enter immediately after breakout candle
- **Enable Conservative Entry**: Wait for pullback to breakout zone
- **Risk:Reward Ratio**: Default 1.5 (1:1.5 risk to reward)
- **Magic Number**: Unique identifier for EA trades
- **Lot Size**: Fixed position size

### Trend Detection
- **Trend EMA Period**: 20-period EMA for trend direction (default: 20)
- **Trailing EMA Period**: 13-period EMA for trailing stops (default: 13)

### MTR Pattern Settings
- **Swing Confirmation Bars**: Bars needed to confirm swing high/low (default: 2)
- **Minimum Break Pips**: Minimum distance Top2 must break above Top1 (default: 3.0)
- **Maximum Fail Pips**: Maximum distance Top3 can exceed Top2 (default: 2.0)
- **Small Body Ratio**: Threshold for small candle bodies (default: 0.4 = 40%)
- **Large Body Ratio**: Threshold for strong breakout candles (default: 0.6 = 60%)
- **Upper Wick Ratio**: Minimum upper wick to body ratio (default: 1.5)
- **Pullback Range**: Range for conservative entry pullbacks (default: 5.0 pips)

### Risk Management
- **Stop Loss Buffer**: Additional pips added to stop loss (default: 2.0)
- **Maximum Spread**: Maximum allowed spread for trading (default: 3.0 pips)

## Trading Rules

### Entry Conditions
1. **Trend Detection**: 20-period EMA determines trend direction
2. **Pattern Recognition**: EA scans for valid MTR structures
3. **Breakout Confirmation**: Strong bearish candle breaks below support
4. **Entry Types**:
   - **Aggressive**: Immediate entry after breakout
   - **Conservative**: Entry on pullback to breakout zone

### Exit Conditions
1. **Stop Loss**: Placed above Top3 high + buffer
2. **Take Profit**: Fixed at 1.5x risk distance
3. **Trailing Stop**: Optional EMA-based trailing (can be enabled)

### Risk Management
- Only one position per symbol allowed
- Spread filtering prevents trading during high spreads
- Position sizing based on fixed lot size (can be enhanced with % risk)

## Recommended Settings

### Timeframes
- **Primary**: H1 (1-hour charts)
- **Alternative**: M30 (30-minute charts)
- **Not recommended**: M1, M5 (too much noise)

### Currency Pairs
- Major pairs: EUR/USD, GBP/USD, USD/JPY, USD/CHF
- Minor pairs: EUR/GBP, AUD/USD, NZD/USD
- Avoid exotic pairs with high spreads

### Market Sessions
- Best during active trading sessions (London, New York)
- Avoid trading during low liquidity periods
- Consider economic news impact

## Monitoring and Alerts

### On-Screen Display
The EA shows real-time information on the chart:
- Current time and spread
- Trend EMA value
- Pattern detection status
- Pullback waiting status
- Number of open positions

### Notifications
- Trade execution alerts
- Pattern detection notifications
- Push notifications to mobile (if enabled)
- Log file entries for all activities

## Troubleshooting

### Common Issues
1. **EA not trading**: Check if live trading is enabled
2. **Compilation errors**: Ensure all include files are present
3. **No patterns detected**: Verify timeframe and market conditions
4. **High spread rejection**: Adjust MaxSpread parameter

### Performance Optimization
- Use VPS for 24/7 operation
- Ensure stable internet connection
- Monitor during different market sessions
- Backtest before live trading

## Backtesting Instructions

### Strategy Tester Setup
1. Open Strategy Tester (Ctrl+R)
2. Select MTR_Expert_Advisor
3. Choose symbol and timeframe
4. Set date range (minimum 3 months)
5. Use "Every tick" or "OHLC" modeling
6. Run test and analyze results

### Optimization
- Test different EMA periods
- Optimize pip distances for different pairs
- Test various risk:reward ratios
- Validate across different market conditions

## Disclaimer
This EA is for educational and research purposes. Past performance does not guarantee future results. Always test thoroughly on demo accounts before live trading. Trading involves substantial risk of loss.

## Support
For questions or issues:
1. Check the log files for error messages
2. Verify all input parameters
3. Test on demo account first
4. Review Al Brooks' price action methodology

## Version History
- v1.00: Initial release with core MTR functionality
- Future updates may include additional features and optimizations
