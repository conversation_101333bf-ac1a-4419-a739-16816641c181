@echo off
echo Testing MTR Expert Advisor compilation...
echo.

REM Check if MetaEditor is available
if exist "C:\Program Files\MetaTrader 5\metaeditor64.exe" (
    echo Found MetaEditor at default location
    "C:\Program Files\MetaTrader 5\metaeditor64.exe" /compile:"MTR_Expert_Advisor.mq5" /log
) else if exist "C:\Program Files (x86)\MetaTrader 5\metaeditor64.exe" (
    echo Found MetaEditor at x86 location
    "C:\Program Files (x86)\MetaTrader 5\metaeditor64.exe" /compile:"MTR_Expert_Advisor.mq5" /log
) else (
    echo MetaEditor not found at default locations
    echo Please compile manually in MetaEditor
    echo 1. Open MetaEditor
    echo 2. Open MTR_Expert_Advisor.mq5
    echo 3. Press F7 to compile
)

echo.
echo Compilation test completed.
pause
