//+------------------------------------------------------------------+
//|                                              MTR_Test_Script.mq5 |
//|                        Copyright 2024, <PERSON> MTR Strategy EA |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON> MTR Strategy EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs
#property description "Test script for MTR Expert Advisor functions"

//--- Input parameters
input int      TestBars = 100;           // Number of bars to analyze
input bool     ShowSwingPoints = true;   // Show swing highs/lows
input bool     ShowEMAValues = true;     // Show EMA values
input bool     ShowCandleAnalysis = true; // Show candle pattern analysis

//--- Global variables
int trendEMA_handle;
double trendEMA_buffer[];

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== MTR Test Script Started ===");
   
   //--- Initialize indicators
   trendEMA_handle = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
   if(trendEMA_handle == INVALID_HANDLE)
   {
      Print("Error: Failed to create EMA indicator");
      return;
   }
   
   ArraySetAsSeries(trendEMA_buffer, true);
   
   //--- Wait for indicator calculation
   Sleep(1000);
   
   if(CopyBuffer(trendEMA_handle, 0, 0, TestBars, trendEMA_buffer) < 0)
   {
      Print("Error: Failed to copy EMA buffer");
      return;
   }
   
   //--- Perform tests
   TestBasicFunctions();
   
   if(ShowSwingPoints)
      TestSwingDetection();
      
   if(ShowEMAValues)
      TestEMAAnalysis();
      
   if(ShowCandleAnalysis)
      TestCandlePatterns();
   
   //--- Cleanup
   IndicatorRelease(trendEMA_handle);
   
   Print("=== MTR Test Script Completed ===");
}

//+------------------------------------------------------------------+
//| Test basic functions                                             |
//+------------------------------------------------------------------+
void TestBasicFunctions()
{
   Print("\n--- Testing Basic Functions ---");
   
   //--- Test spread calculation
   double spread = GetSpreadInPips();
   Print("Current Spread: ", DoubleToString(spread, 1), " pips");
   
   //--- Test trend direction
   ENUM_TREND_DIRECTION trend = GetTrendDirection();
   string trendStr = (trend == TREND_UP) ? "UP" : 
                     (trend == TREND_DOWN) ? "DOWN" : "SIDEWAYS";
   Print("Current Trend: ", trendStr);
   
   //--- Test market status
   bool marketOpen = IsMarketOpen();
   Print("Market Open: ", marketOpen ? "YES" : "NO");
   
   //--- Test time functions
   Print("Current Time: ", GetTimeString());
}

//+------------------------------------------------------------------+
//| Test swing point detection                                       |
//+------------------------------------------------------------------+
void TestSwingDetection()
{
   Print("\n--- Testing Swing Point Detection ---");
   
   //--- Find recent swing highs
   for(int i = 0; i < 5; i++)
   {
      int swingIndex = FindSwingHigh(2, 20 + i * 10);
      if(swingIndex > 0)
      {
         datetime swingTime = iTime(_Symbol, PERIOD_CURRENT, swingIndex);
         double swingPrice = iHigh(_Symbol, PERIOD_CURRENT, swingIndex);
         Print("Swing High #", i+1, ": Bar ", swingIndex, 
               " Time: ", TimeToString(swingTime), 
               " Price: ", DoubleToString(swingPrice, _Digits));
      }
   }
}

//+------------------------------------------------------------------+
//| Test EMA analysis                                                |
//+------------------------------------------------------------------+
void TestEMAAnalysis()
{
   Print("\n--- Testing EMA Analysis ---");
   
   for(int i = 1; i <= 5; i++)
   {
      double price = iClose(_Symbol, PERIOD_CURRENT, i);
      double ema = trendEMA_buffer[i];
      double diff = (price - ema) / _Point;
      
      Print("Bar ", i, ": Price=", DoubleToString(price, _Digits),
            " EMA=", DoubleToString(ema, _Digits),
            " Diff=", DoubleToString(diff, 1), " pips");
   }
}

//+------------------------------------------------------------------+
//| Test candle pattern analysis                                     |
//+------------------------------------------------------------------+
void TestCandlePatterns()
{
   Print("\n--- Testing Candle Pattern Analysis ---");
   
   for(int i = 1; i <= 10; i++)
   {
      bool smallBody = HasSmallBodyLongWick(i);
      bool strongBearish = HasStrongBearishBody(i);
      bool indecision = HasIndecisionCandle(i);
      bool bearishEngulf = IsBearishEngulfing(i);
      
      if(smallBody || strongBearish || indecision || bearishEngulf)
      {
         datetime barTime = iTime(_Symbol, PERIOD_CURRENT, i);
         string patterns = "";
         
         if(smallBody) patterns += "SmallBody ";
         if(strongBearish) patterns += "StrongBearish ";
         if(indecision) patterns += "Indecision ";
         if(bearishEngulf) patterns += "BearishEngulf ";
         
         Print("Bar ", i, " (", TimeToString(barTime), "): ", patterns);
      }
   }
}

//+------------------------------------------------------------------+
//| Helper functions (copied from main EA)                          |
//+------------------------------------------------------------------+

double GetSpreadInPips()
{
   return (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
}

enum ENUM_TREND_DIRECTION
{
   TREND_UP,
   TREND_DOWN,
   TREND_SIDEWAYS
};

ENUM_TREND_DIRECTION GetTrendDirection()
{
   double currentPrice = iClose(_Symbol, PERIOD_CURRENT, 1);
   double ema_current = trendEMA_buffer[1];
   double ema_previous = trendEMA_buffer[5];
   
   bool priceAboveEMA = currentPrice > ema_current;
   bool emaRising = ema_current > ema_previous;
   
   if(priceAboveEMA && emaRising)
      return TREND_UP;
   else if(!priceAboveEMA && !emaRising)
      return TREND_DOWN;
   else
      return TREND_SIDEWAYS;
}

bool IsMarketOpen()
{
   return SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL;
}

string GetTimeString()
{
   return TimeToString(TimeCurrent(), TIME_DATE | TIME_MINUTES);
}

int FindSwingHigh(int confirmBars, int lookbackBars)
{
   for(int i = confirmBars + 1; i < lookbackBars; i++)
   {
      double currentHigh = iHigh(_Symbol, PERIOD_CURRENT, i);
      bool isSwingHigh = true;
      
      for(int j = 1; j <= confirmBars; j++)
      {
         if(iHigh(_Symbol, PERIOD_CURRENT, i - j) >= currentHigh)
         {
            isSwingHigh = false;
            break;
         }
      }
      
      if(isSwingHigh)
      {
         for(int j = 1; j <= confirmBars; j++)
         {
            if(iHigh(_Symbol, PERIOD_CURRENT, i + j) >= currentHigh)
            {
               isSwingHigh = false;
               break;
            }
         }
      }
      
      if(isSwingHigh)
         return i;
   }
   return -1;
}

bool HasSmallBodyLongWick(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);
   
   double bodySize = MathAbs(close - open);
   double totalSize = high - low;
   double upperWick = high - MathMax(open, close);
   
   if(totalSize == 0) return false;
   
   double bodyRatio = bodySize / totalSize;
   double wickRatio = (bodySize > 0) ? upperWick / bodySize : 0;
   
   return (bodyRatio < 0.4 && wickRatio > 1.5);
}

bool HasStrongBearishBody(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);
   
   if(close >= open) return false;
   
   double bodySize = open - close;
   double totalSize = high - low;
   
   if(totalSize == 0) return false;
   
   double bodyRatio = bodySize / totalSize;
   return (bodyRatio > 0.6);
}

bool HasIndecisionCandle(int index)
{
   double open = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close = iClose(_Symbol, PERIOD_CURRENT, index);
   double high = iHigh(_Symbol, PERIOD_CURRENT, index);
   double low = iLow(_Symbol, PERIOD_CURRENT, index);
   
   double bodySize = MathAbs(close - open);
   double totalSize = high - low;
   
   if(totalSize == 0) return false;
   
   double bodyRatio = bodySize / totalSize;
   return (bodyRatio < 0.4);
}

bool IsBearishEngulfing(int index)
{
   if(index >= Bars(_Symbol, PERIOD_CURRENT) - 1) return false;
   
   double open1 = iOpen(_Symbol, PERIOD_CURRENT, index + 1);
   double close1 = iClose(_Symbol, PERIOD_CURRENT, index + 1);
   double open2 = iOpen(_Symbol, PERIOD_CURRENT, index);
   double close2 = iClose(_Symbol, PERIOD_CURRENT, index);
   
   return (close1 > open1 && close2 < open2 && open2 > close1 && close2 < open1);
}
